%% 修改Simulink模型以复现图片中的波形
% 这个脚本用于修改bxht13.slx模型来生成类似的波形

function modify_simulink_model()
    %% 打开模型
    model_name = 'bxht13';
    
    try
        % 加载模型
        load_system(model_name);
        fprintf('成功加载模型: %s\n', model_name);
    catch ME
        fprintf('加载模型失败: %s\n', ME.message);
        return;
    end
    
    %% 设置仿真参数
    set_param(model_name, 'StopTime', '0.6');           % 仿真时间0.6秒
    set_param(model_name, 'Solver', 'ode23tb');         % 适合电力电子的求解器
    set_param(model_name, 'MaxStep', '1e-6');           % 最大步长
    set_param(model_name, 'RelTol', '1e-4');            % 相对误差
    
    %% 添加负载阶跃功能
    add_load_step_functionality(model_name);
    
    %% 添加控制模式切换功能
    add_mode_switch_functionality(model_name);
    
    %% 配置示波器
    configure_scopes(model_name);
    
    %% 保存修改后的模型
    save_system(model_name, [model_name '_modified']);
    fprintf('修改后的模型已保存为: %s_modified.slx\n', model_name);
    
    %% 运行仿真
    fprintf('开始仿真...\n');
    sim(model_name);
    fprintf('仿真完成！\n');
    
end

function add_load_step_functionality(model_name)
    %% 添加负载阶跃功能
    
    % 查找现有的负载电阻
    load_blocks = find_system(model_name, 'BlockType', 'Reference', ...
        'SourceBlock', 'spsSeriesRLCBranchLib/Series RLC Branch');
    
    if ~isempty(load_blocks)
        % 找到RL负载块
        rl_block = '';
        for i = 1:length(load_blocks)
            block_name = get_param(load_blocks{i}, 'Name');
            if contains(block_name, 'RL')
                rl_block = load_blocks{i};
                break;
            end
        end
        
        if ~isempty(rl_block)
            % 添加Step信号源来控制负载
            step_block = [model_name '/Load_Step'];
            
            try
                % 添加Step块
                add_block('simulink/Sources/Step', step_block);
                
                % 设置Step参数
                set_param(step_block, 'Time', '0.3');        % 阶跃时间
                set_param(step_block, 'Before', '30');       % 初始值30Ω
                set_param(step_block, 'After', '50');        % 阶跃后50Ω
                
                % 添加Gain块来控制负载电阻
                gain_block = [model_name '/Load_Gain'];
                add_block('simulink/Math Operations/Gain', gain_block);
                set_param(gain_block, 'Gain', '1');
                
                % 连接信号
                add_line(model_name, 'Load_Step/1', 'Load_Gain/1');
                
                fprintf('成功添加负载阶跃功能\n');
                
            catch ME
                fprintf('添加负载阶跃功能失败: %s\n', ME.message);
            end
        end
    end
end

function add_mode_switch_functionality(model_name)
    %% 添加控制模式切换功能
    
    % 查找控制器子系统
    controller_blocks = find_system(model_name, 'BlockType', 'SubSystem', ...
        'Name', 'Controller');
    
    if ~isempty(controller_blocks)
        controller_block = controller_blocks{1};
        
        try
            % 添加模式切换信号
            mode_switch_block = [model_name '/Mode_Switch'];
            add_block('simulink/Sources/Step', mode_switch_block);
            
            % 设置模式切换参数
            set_param(mode_switch_block, 'Time', '0.4');     % 切换时间
            set_param(mode_switch_block, 'Before', '0');     % 不可控模式
            set_param(mode_switch_block, 'After', '1');      % 可控模式
            
            % 添加Switch块来切换控制策略
            switch_block = [model_name '/Control_Switch'];
            add_block('simulink/Signal Routing/Switch', switch_block);
            
            fprintf('成功添加控制模式切换功能\n');
            
        catch ME
            fprintf('添加控制模式切换功能失败: %s\n', ME.message);
        end
    end
end

function configure_scopes(model_name)
    %% 配置示波器显示
    
    % 查找所有Scope块
    scope_blocks = find_system(model_name, 'BlockType', 'Scope');
    
    for i = 1:length(scope_blocks)
        try
            % 设置示波器参数
            set_param(scope_blocks{i}, 'TimeRange', '0.6');
            set_param(scope_blocks{i}, 'YMin', '-500');
            set_param(scope_blocks{i}, 'YMax', '500');
            set_param(scope_blocks{i}, 'DataLogging', 'on');
            
        catch ME
            fprintf('配置示波器 %s 失败: %s\n', scope_blocks{i}, ME.message);
        end
    end
    
    fprintf('示波器配置完成\n');
end

%% 辅助函数：创建波形分析脚本
function create_waveform_analysis_script()
    %% 创建波形分析脚本
    
    script_content = {
        '%% 波形分析脚本'
        'function analyze_waveforms()'
        ''
        '% 获取仿真数据'
        'simout = evalin(''base'', ''simout'');'
        ''
        '% 提取时间和信号数据'
        'if exist(''simout'', ''var'')'
        '    % 这里添加具体的数据提取代码'
        '    % 根据实际的信号名称调整'
        '    fprintf(''仿真数据分析完成\n'');'
        'else'
        '    fprintf(''未找到仿真数据\n'');'
        'end'
        ''
        'end'
    };
    
    % 写入文件
    fid = fopen('analyze_waveforms.m', 'w');
    for i = 1:length(script_content)
        fprintf(fid, '%s\n', script_content{i});
    end
    fclose(fid);
    
    fprintf('波形分析脚本已创建: analyze_waveforms.m\n');
end

%% 主函数调用
if exist('bxht13.slx', 'file')
    modify_simulink_model();
    create_waveform_analysis_script();
else
    fprintf('未找到模型文件 bxht13.slx\n');
    fprintf('请确保文件在当前工作目录中\n');
end
