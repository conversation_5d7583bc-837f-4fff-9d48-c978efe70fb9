%% 模拟图片中的波形 - AC-DC变换器仿真
% 复现step-down of load和mode transition的波形

clear all; close all; clc;

%% 仿真参数设置
fs = 5000;          % 开关频率 5kHz
f_line = 50;        % 电网频率 50Hz
Ts = 1/fs;          % 开关周期
dt = Ts/100;        % 仿真步长
t_total = 0.6;      % 总仿真时间 0.6秒
t = 0:dt:t_total;   % 时间向量

% 电路参数
Vac_peak = 311;     % AC电压峰值
Vdc_ref = 400;      % 直流电压参考值
L = 2.5e-3;         % 电感值 2.5mH
C = 100e-3;         % 电容值 100mF
R_load1 = 30;       % 初始负载电阻
R_load2 = 50;       % 阶跃后负载电阻

%% 生成基本波形
% AC电压源
vac = Vac_peak * sin(2*pi*f_line*t);

% 直流电压 (简化模型，实际会有纹波)
vdc = Vdc_ref * ones(size(t));

% 负载阶跃时间点
t_step = 0.3;       % 在0.3秒时负载阶跃
step_idx = find(t >= t_step, 1);

% 负载电阻变化
R_load = R_load1 * ones(size(t));
R_load(step_idx:end) = R_load2;

%% 生成开关控制信号 (简化的PWM信号)
% 这里简化为方波，实际应该是PWM调制
duty_cycle = 0.5;   % 占空比
switch_period = 1/fs;
uab = zeros(size(t));

for i = 1:length(t)
    cycle_time = mod(t(i), switch_period);
    if cycle_time < duty_cycle * switch_period
        uab(i) = 1;
    else
        uab(i) = 0;
    end
end

% 将开关信号调整为合适的幅值显示
uab = uab * 100 - 50;  % 调整到-50到50的范围

%% 生成电流波形 (简化模型)
% 输入电流 - 考虑功率因数校正
P_load = vdc.^2 ./ R_load;  % 负载功率
iac_envelope = P_load ./ (Vac_peak/sqrt(2));  % 电流包络

% 添加开关纹波
iac = zeros(size(t));
for i = 1:length(t)
    % 基本正弦电流
    base_current = iac_envelope(i) * sin(2*pi*f_line*t(i));
    
    % 添加开关频率纹波
    ripple = 0.1 * base_current * sin(2*pi*fs*t(i));
    
    iac(i) = base_current + ripple;
end

%% 模式切换仿真 (第二个图)
% 在0.4秒时从不可控模式切换到可控模式
t_mode_switch = 0.4;
mode_idx = find(t >= t_mode_switch, 1);

% 复制波形用于模式切换仿真
vac2 = vac;
vdc2 = vdc;
iac2 = iac;
uab2 = uab;

% 在模式切换后改变控制策略
% 不可控模式：较大的电流纹波
% 可控模式：较小的电流纹波，更好的功率因数
for i = mode_idx:length(t)
    % 改善后的电流波形
    base_current = iac_envelope(i) * sin(2*pi*f_line*t(i));
    ripple = 0.05 * base_current * sin(2*pi*fs*t(i));  % 减小纹波
    iac2(i) = base_current + ripple;
    
    % 改变开关模式
    cycle_time = mod(t(i), switch_period);
    if cycle_time < 0.6 * switch_period  % 改变占空比
        uab2(i) = 100 - 50;
    else
        uab2(i) = 0 - 50;
    end
end

%% 绘制波形图
figure('Position', [100, 100, 1200, 800]);

% 子图1: step-down of load
subplot(2,1,1);
hold on;
plot(t*1000, vdc, 'Color', [1, 0.5, 0], 'LineWidth', 2, 'DisplayName', 'u_{dc}');
plot(t*1000, vac/2, 'b', 'LineWidth', 1.5, 'DisplayName', 'u_s');
plot(t*1000, iac*10, 'r', 'LineWidth', 1.5, 'DisplayName', 'i_s');
plot(t*1000, uab, 'g', 'LineWidth', 1, 'DisplayName', 'u_{ab}');

% 添加负载阶跃标记
xline(t_step*1000, '--k', 'LineWidth', 2, 'Alpha', 0.7);
text(t_step*1000+20, 300, '负载阶跃', 'FontSize', 12, 'Color', 'k');

xlabel('时间 (ms)');
ylabel('幅值');
title('(b) step-down of load');
legend('Location', 'best');
grid on;
xlim([0, t_total*1000]);
ylim([-200, 450]);

% 子图2: uncontrollable mode to controllable
subplot(2,1,2);
hold on;
plot(t*1000, vdc2, 'Color', [1, 0.5, 0], 'LineWidth', 2, 'DisplayName', 'u_{dc}');
plot(t*1000, vac2/2, 'b', 'LineWidth', 1.5, 'DisplayName', 'u_s');
plot(t*1000, iac2*10, 'r', 'LineWidth', 1.5, 'DisplayName', 'i_s');
plot(t*1000, uab2, 'g', 'LineWidth', 1, 'DisplayName', 'u_{ab}');

% 添加模式切换标记
xline(t_mode_switch*1000, '--k', 'LineWidth', 2, 'Alpha', 0.7);
text(t_mode_switch*1000+20, 300, '模式切换', 'FontSize', 12, 'Color', 'k');

xlabel('时间 (ms)');
ylabel('幅值');
title('(c) uncontrollable mode to controllable');
legend('Location', 'best');
grid on;
xlim([0, t_total*1000]);
ylim([-200, 450]);

% 调整子图间距
sgtitle('AC-DC变换器波形仿真', 'FontSize', 16, 'FontWeight', 'bold');

%% 保存图片
saveas(gcf, 'simulated_waveforms.png');
fprintf('波形仿真完成！图片已保存为 simulated_waveforms.png\n');

%% 显示关键参数
fprintf('\n=== 仿真参数 ===\n');
fprintf('开关频率: %d Hz\n', fs);
fprintf('电网频率: %d Hz\n', f_line);
fprintf('AC电压峰值: %d V\n', Vac_peak);
fprintf('DC电压参考: %d V\n', Vdc_ref);
fprintf('负载阶跃时间: %.1f s\n', t_step);
fprintf('模式切换时间: %.1f s\n', t_mode_switch);
fprintf('初始负载: %d Ω\n', R_load1);
fprintf('阶跃后负载: %d Ω\n', R_load2);
