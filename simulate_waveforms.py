#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟图片中的波形 - AC-DC变换器仿真
复现step-down of load和mode transition的波形
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
matplotlib.rcParams['axes.unicode_minus'] = False

def simulate_waveforms():
    """模拟AC-DC变换器的波形"""
    
    # 仿真参数设置
    fs = 5000          # 开关频率 5kHz
    f_line = 50        # 电网频率 50Hz
    Ts = 1/fs          # 开关周期
    dt = Ts/100        # 仿真步长
    t_total = 0.6      # 总仿真时间 0.6秒
    t = np.arange(0, t_total, dt)
    
    # 电路参数
    Vac_peak = 311     # AC电压峰值
    Vdc_ref = 400      # 直流电压参考值
    L = 2.5e-3         # 电感值 2.5mH
    C = 100e-3         # 电容值 100mF
    R_load1 = 30       # 初始负载电阻
    R_load2 = 50       # 阶跃后负载电阻
    
    # 生成基本波形
    # AC电压源
    vac = Vac_peak * np.sin(2*np.pi*f_line*t)
    
    # 直流电压 (简化模型，实际会有纹波)
    vdc = Vdc_ref * np.ones_like(t)
    
    # 负载阶跃时间点
    t_step = 0.3       # 在0.3秒时负载阶跃
    step_idx = np.where(t >= t_step)[0][0] if len(np.where(t >= t_step)[0]) > 0 else len(t)
    
    # 负载电阻变化
    R_load = R_load1 * np.ones_like(t)
    R_load[step_idx:] = R_load2
    
    # 生成开关控制信号 (简化的PWM信号)
    duty_cycle = 0.5   # 占空比
    switch_period = 1/fs
    uab = np.zeros_like(t)
    
    for i, time in enumerate(t):
        cycle_time = time % switch_period
        if cycle_time < duty_cycle * switch_period:
            uab[i] = 1
        else:
            uab[i] = 0
    
    # 将开关信号调整为合适的幅值显示
    uab = uab * 100 - 50  # 调整到-50到50的范围
    
    # 生成电流波形 (简化模型)
    # 输入电流 - 考虑功率因数校正
    P_load = vdc**2 / R_load  # 负载功率
    iac_envelope = P_load / (Vac_peak/np.sqrt(2))  # 电流包络
    
    # 添加开关纹波
    iac = np.zeros_like(t)
    for i, time in enumerate(t):
        # 基本正弦电流
        base_current = iac_envelope[i] * np.sin(2*np.pi*f_line*time)
        
        # 添加开关频率纹波
        ripple = 0.1 * base_current * np.sin(2*np.pi*fs*time)
        
        iac[i] = base_current + ripple
    
    # 模式切换仿真 (第二个图)
    # 在0.4秒时从不可控模式切换到可控模式
    t_mode_switch = 0.4
    mode_idx = np.where(t >= t_mode_switch)[0][0] if len(np.where(t >= t_mode_switch)[0]) > 0 else len(t)
    
    # 复制波形用于模式切换仿真
    vac2 = vac.copy()
    vdc2 = vdc.copy()
    iac2 = iac.copy()
    uab2 = uab.copy()
    
    # 在模式切换后改变控制策略
    for i in range(mode_idx, len(t)):
        # 改善后的电流波形
        base_current = iac_envelope[i] * np.sin(2*np.pi*f_line*t[i])
        ripple = 0.05 * base_current * np.sin(2*np.pi*fs*t[i])  # 减小纹波
        iac2[i] = base_current + ripple
        
        # 改变开关模式
        cycle_time = t[i] % switch_period
        if cycle_time < 0.6 * switch_period:  # 改变占空比
            uab2[i] = 100 - 50
        else:
            uab2[i] = 0 - 50
    
    return t, vac, vdc, iac, uab, vac2, vdc2, iac2, uab2, t_step, t_mode_switch

def plot_waveforms():
    """绘制波形图"""
    
    # 获取仿真数据
    t, vac, vdc, iac, uab, vac2, vdc2, iac2, uab2, t_step, t_mode_switch = simulate_waveforms()
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 子图1: step-down of load
    ax1.plot(t*1000, vdc, color='orange', linewidth=2, label='u_dc')
    ax1.plot(t*1000, vac/2, 'b', linewidth=1.5, label='u_s')
    ax1.plot(t*1000, iac*10, 'r', linewidth=1.5, label='i_s')
    ax1.plot(t*1000, uab, 'g', linewidth=1, label='u_ab')
    
    # 添加负载阶跃标记
    ax1.axvline(x=t_step*1000, color='k', linestyle='--', linewidth=2, alpha=0.7)
    ax1.text(t_step*1000+20, 300, '负载阶跃', fontsize=12, color='k')
    
    ax1.set_xlabel('时间 (ms)')
    ax1.set_ylabel('幅值')
    ax1.set_title('(b) step-down of load')
    ax1.legend()
    ax1.grid(True)
    ax1.set_xlim([0, len(t)*1000*0.001])
    ax1.set_ylim([-200, 450])
    
    # 子图2: uncontrollable mode to controllable
    ax2.plot(t*1000, vdc2, color='orange', linewidth=2, label='u_dc')
    ax2.plot(t*1000, vac2/2, 'b', linewidth=1.5, label='u_s')
    ax2.plot(t*1000, iac2*10, 'r', linewidth=1.5, label='i_s')
    ax2.plot(t*1000, uab2, 'g', linewidth=1, label='u_ab')
    
    # 添加模式切换标记
    ax2.axvline(x=t_mode_switch*1000, color='k', linestyle='--', linewidth=2, alpha=0.7)
    ax2.text(t_mode_switch*1000+20, 300, '模式切换', fontsize=12, color='k')
    
    ax2.set_xlabel('时间 (ms)')
    ax2.set_ylabel('幅值')
    ax2.set_title('(c) uncontrollable mode to controllable')
    ax2.legend()
    ax2.grid(True)
    ax2.set_xlim([0, len(t)*1000*0.001])
    ax2.set_ylim([-200, 450])
    
    # 调整布局
    plt.tight_layout()
    plt.suptitle('AC-DC变换器波形仿真', fontsize=16, fontweight='bold', y=0.98)
    
    # 保存图片
    plt.savefig('simulated_waveforms.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("波形仿真完成！图片已保存为 simulated_waveforms.png")
    
    # 显示关键参数
    print("\n=== 仿真参数 ===")
    print(f"开关频率: 5000 Hz")
    print(f"电网频率: 50 Hz")
    print(f"AC电压峰值: 311 V")
    print(f"DC电压参考: 400 V")
    print(f"负载阶跃时间: {t_step} s")
    print(f"模式切换时间: {t_mode_switch} s")
    print(f"初始负载: 30 Ω")
    print(f"阶跃后负载: 50 Ω")

if __name__ == "__main__":
    plot_waveforms()
