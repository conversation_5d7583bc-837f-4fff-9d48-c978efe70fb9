# AC-DC变换器波形复现项目

## 项目概述
本项目旨在复现您提供的图片中显示的AC-DC变换器波形，包括：
- (b) step-down of load - 负载阶跃下降
- (c) uncontrollable mode to controllable - 从不可控模式到可控模式的转换

## 文件说明

### 1. 原始Simulink模型
- `bxht13.slx` - 您的原始Simulink模型文件

### 2. Python仿真脚本
- `simulate_waveforms.py` - Python版本的波形仿真脚本
- `simulated_waveforms.png` - 生成的波形图片

### 3. MATLAB脚本
- `simulate_waveforms.m` - MATLAB版本的波形仿真脚本
- `modify_simulink_model.m` - 用于修改Simulink模型的脚本

### 4. 文档
- `simulink_analysis_guide.md` - 详细的Simulink模型分析指南
- `README.md` - 本说明文档

## 使用方法

### 方法1: 使用Python脚本（推荐）
如果您的系统中没有MATLAB，可以使用Python脚本：

```bash
python simulate_waveforms.py
```

这将生成类似原图的波形并保存为PNG文件。

### 方法2: 使用MATLAB
如果您有MATLAB和Simulink：

1. 打开MATLAB
2. 切换到项目目录
3. 运行仿真脚本：
   ```matlab
   run('simulate_waveforms.m')
   ```

4. 或者修改原始Simulink模型：
   ```matlab
   run('modify_simulink_model.m')
   ```

### 方法3: 直接在Simulink中操作
1. 打开`bxht13.slx`
2. 按照`simulink_analysis_guide.md`中的指导进行参数调整
3. 运行仿真

## 波形特征

### 负载阶跃波形 (step-down of load)
- **时间**: 在0.3秒时发生负载从30Ω到50Ω的阶跃
- **udc**: 直流电压保持稳定
- **us**: 50Hz交流输入电压
- **is**: 输入电流在负载阶跃时幅值变化
- **uab**: PWM开关控制信号

### 模式切换波形 (uncontrollable to controllable)
- **时间**: 在0.4秒时从不可控模式切换到可控模式
- **特征**: 电流纹波减小，功率因数改善

## 关键参数

### 电路参数
- AC电压峰值: 311V (220V RMS)
- 频率: 50Hz
- 开关频率: 5kHz
- 电感: 2.5mH
- 电容: 100mF
- 负载电阻: 30Ω → 50Ω

### 仿真参数
- 仿真时间: 0.6秒
- 负载阶跃时间: 0.3秒
- 模式切换时间: 0.4秒

## 结果文件
运行脚本后会生成：
- `simulated_waveforms.png` - 波形图片
- 控制台输出显示仿真参数

## 故障排除

### Python脚本问题
如果遇到导入错误，请安装必要的库：
```bash
pip install numpy matplotlib
```

### MATLAB问题
1. 确保安装了Simulink和Power System Toolbox
2. 检查模型文件路径是否正确
3. 确保有足够的许可证

### 波形不匹配
如果生成的波形与原图不完全一致：
1. 调整电路参数
2. 修改控制策略
3. 优化仿真设置

## 进一步改进

### 可能的改进方向
1. **更精确的控制算法**: 实现真实的PFC控制
2. **更详细的电路模型**: 包含寄生参数
3. **实际测量数据**: 与实验结果对比
4. **参数优化**: 自动调整参数匹配目标波形

### 扩展功能
1. 添加更多工作模式
2. 实现闭环控制
3. 添加故障模拟
4. 效率分析

## 联系信息
如果您需要进一步的帮助或有任何问题，请随时联系。

---
*注意：本项目基于对原始Simulink模型的分析和理解，生成的波形是近似的。要获得完全一致的结果，可能需要根据具体的控制策略和电路参数进行进一步调整。*
